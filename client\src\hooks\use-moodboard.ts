/**
 * Custom hook for moodboard operations with Supabase integration
 * Follows the same pattern as other tool hooks in the application
 */

import { useState, useEffect, useCallback } from 'react'
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { useToast } from '@/hooks/use-toast'
import { useAuth } from '@/hooks/use-auth'
import moodboardService, { 
  CreateMoodboardRequest, 
  UpdateMoodboardRequest,
  MoodboardListResponse,
  MoodboardOperationResponse,
  MoodboardStatsResponse
} from '@/services/moodboard-service'
import type { Moodboard } from '@/lib/supabase'

export interface UseMoodboardOptions {
  autoSaveInterval?: number // Auto-save interval in milliseconds
  enableAutoSave?: boolean
}

export function useMoodboard(moodboardId?: string, options: UseMoodboardOptions = {}) {
  const { toast } = useToast()
  const { user } = useAuth()
  const queryClient = useQueryClient()
  
  const { autoSaveInterval = 30000, enableAutoSave = true } = options
  
  // Auto-save state
  const [autoSaveTimer, setAutoSaveTimer] = useState<NodeJS.Timeout | null>(null)
  const [lastSaved, setLastSaved] = useState<Date | null>(null)
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false)

  // Query for getting a specific moodboard
  const {
    data: moodboard,
    isLoading: isLoadingMoodboard,
    error: moodboardError,
    refetch: refetchMoodboard
  } = useQuery({
    queryKey: ['moodboard', moodboardId],
    queryFn: async () => {
      if (!moodboardId || moodboardId === 'new') return null
      const response = await moodboardService.getMoodboard(moodboardId)
      return response.data as Moodboard
    },
    enabled: !!moodboardId && moodboardId !== 'new' && !!user,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  // Query for listing moodboards
  const {
    data: moodboardList,
    isLoading: isLoadingList,
    error: listError,
    refetch: refetchList
  } = useQuery({
    queryKey: ['moodboards', 'list'],
    queryFn: async () => {
      const response = await moodboardService.listMoodboards()
      return response as MoodboardListResponse
    },
    enabled: !!user,
    staleTime: 2 * 60 * 1000, // 2 minutes
  })

  // Query for moodboard stats
  const {
    data: stats,
    isLoading: isLoadingStats,
    error: statsError,
    refetch: refetchStats
  } = useQuery({
    queryKey: ['moodboards', 'stats'],
    queryFn: async () => {
      const response = await moodboardService.getMoodboardStats()
      return response as MoodboardStatsResponse
    },
    enabled: !!user,
    staleTime: 5 * 60 * 1000, // 5 minutes
  })

  // Mutation for creating a moodboard
  const createMoodboardMutation = useMutation({
    mutationFn: async (data: CreateMoodboardRequest) => {
      const response = await moodboardService.createMoodboard(data)
      return response
    },
    onSuccess: (response) => {
      toast({
        title: "Moodboard creado",
        description: "Tu moodboard ha sido creado exitosamente.",
      })
      
      // Invalidate and refetch queries - be more specific for better cache management
      queryClient.invalidateQueries({ queryKey: ['moodboards', 'list'] })
      queryClient.invalidateQueries({ queryKey: ['moodboards', 'stats'] })
      setLastSaved(new Date())
      setHasUnsavedChanges(false)
      
      return response.data
    },
    onError: (error: Error) => {
      toast({
        title: "Error al crear moodboard",
        description: error.message || "Ocurrió un error inesperado.",
        variant: "destructive",
      })
    },
  })

  // Mutation for updating a moodboard
  const updateMoodboardMutation = useMutation({
    mutationFn: async ({ id, data }: { id: string; data: UpdateMoodboardRequest }) => {
      const response = await moodboardService.updateMoodboard(id, data)
      return response
    },
    onSuccess: (response, variables) => {
      // Only show toast for manual saves, not auto-saves
      if (!variables.data.tldraw_data || variables.data.title || variables.data.description) {
        toast({
          title: "Moodboard actualizado",
          description: "Los cambios han sido guardados exitosamente.",
        })
      }
      
      // Invalidate and refetch queries - be more specific for better cache management
      queryClient.invalidateQueries({ queryKey: ['moodboard', variables.id] })
      queryClient.invalidateQueries({ queryKey: ['moodboards', 'list'] })
      queryClient.invalidateQueries({ queryKey: ['moodboards', 'stats'] })
      setLastSaved(new Date())
      setHasUnsavedChanges(false)
    },
    onError: (error: Error) => {
      toast({
        title: "Error al actualizar moodboard",
        description: error.message || "Ocurrió un error inesperado.",
        variant: "destructive",
      })
    },
  })

  // Mutation for deleting a moodboard
  const deleteMoodboardMutation = useMutation({
    mutationFn: async (id: string) => {
      const response = await moodboardService.deleteMoodboard(id)
      return response
    },
    onSuccess: () => {
      toast({
        title: "Moodboard eliminado",
        description: "El moodboard ha sido eliminado exitosamente.",
      })
      
      // Invalidate and refetch queries - be more specific for better cache management
      queryClient.invalidateQueries({ queryKey: ['moodboards', 'list'] })
      queryClient.invalidateQueries({ queryKey: ['moodboards', 'stats'] })
    },
    onError: (error: Error) => {
      toast({
        title: "Error al eliminar moodboard",
        description: error.message || "Ocurrió un error inesperado.",
        variant: "destructive",
      })
    },
  })

  // Auto-save function
  const autoSave = useCallback(async (id: string, tldrawData: any) => {
    if (!enableAutoSave || !id || id === 'new') return
    
    try {
      await moodboardService.autoSaveMoodboard(id, tldrawData)
      setLastSaved(new Date())
      setHasUnsavedChanges(false)
    } catch (error) {
      console.error('Auto-save failed:', error)
      // Don't show error toast for auto-save failures
    }
  }, [enableAutoSave])

  // Schedule auto-save
  const scheduleAutoSave = useCallback((id: string, tldrawData: any) => {
    if (!enableAutoSave) return
    
    // Clear existing timer
    if (autoSaveTimer) {
      clearTimeout(autoSaveTimer)
    }
    
    // Set unsaved changes flag
    setHasUnsavedChanges(true)
    
    // Schedule new auto-save
    const timer = setTimeout(() => {
      autoSave(id, tldrawData)
    }, autoSaveInterval)
    
    setAutoSaveTimer(timer)
  }, [autoSave, autoSaveInterval, autoSaveTimer, enableAutoSave])

  // Manual save function
  const manualSave = useCallback(async (id: string, data: UpdateMoodboardRequest) => {
    if (!id || id === 'new') return
    
    // Clear auto-save timer since we're doing a manual save
    if (autoSaveTimer) {
      clearTimeout(autoSaveTimer)
      setAutoSaveTimer(null)
    }
    
    return updateMoodboardMutation.mutateAsync({ id, data })
  }, [updateMoodboardMutation, autoSaveTimer])

  // Cleanup auto-save timer on unmount
  useEffect(() => {
    return () => {
      if (autoSaveTimer) {
        clearTimeout(autoSaveTimer)
      }
    }
  }, [autoSaveTimer])

  return {
    // Data
    moodboard,
    moodboardList: moodboardList?.moodboards || [],
    stats,
    
    // Loading states
    isLoadingMoodboard,
    isLoadingList,
    isLoadingStats,
    isCreating: createMoodboardMutation.isPending,
    isUpdating: updateMoodboardMutation.isPending,
    isDeleting: deleteMoodboardMutation.isPending,
    
    // Error states
    moodboardError,
    listError,
    statsError,
    
    // Auto-save state
    lastSaved,
    hasUnsavedChanges,
    
    // Actions
    createMoodboard: createMoodboardMutation.mutateAsync,
    updateMoodboard: updateMoodboardMutation.mutateAsync,
    deleteMoodboard: deleteMoodboardMutation.mutateAsync,
    scheduleAutoSave,
    manualSave,
    
    // Refetch functions
    refetchMoodboard,
    refetchList,
    refetchStats,
  }
}
